# -*- coding: utf-8 -*-
from odoo import models, fields, api, _


class BidWonOrder(models.Model):
    _inherit = 'bid.won.order'

    # 流程调整暂时屏蔽
    # def action_create_po(self):
    #     """
    #     在已存在装箱单、采购单的情况下，把采购单删除重新生成后，因自动寻找未被关联的装箱单并将其关联
    #     """
    #     result = super(BidWonOrder, self).action_create_po()
    #
    #     invoice_id = self.invoice_id
    #
    #     packing_list = self.env['packing.list'].search([('galaxy_vendor_invoice_ids', 'in', invoice_id.ids)])
    #     if len(packing_list) == 1:
    #         packing_list.write({'purchase_ids': [(4, self.po_id.id)]})
    #     return result

    def prepare_analysis_item_map(self, won_orders, purchases=None, filter_glot_numbers=None):
        """
        从中标单line item为基准, 统计出中标和采购数量
        """
        analysis_item_map = {}
        glot_won_vpi_line_map = {}

        # 1. 从中标单line item为基准生成
        # won_orders = self.galaxy_vendor_invoice_ids.won_order_id
        if not filter_glot_numbers:
            won_lots = won_orders.mapped('order_line_ids')
        else:
            won_lots = self.env['bid.won.order.line'].search([('glot_number', 'in', filter_glot_numbers), ('bid_won_order_id', 'in', won_orders.ids)])
        
        for won_lot in won_lots:
            glot_number = won_lot.glot_number
            vpi_line = won_lot.invoice_lot_ids[0]
            if vpi_line.line_type != 'normal':
                continue
            glot_won_vpi_line_map[glot_number] = vpi_line
            for line in won_lot.line_detail_ids:
                strand_sku = line.sku
                sku = strand_sku.internal_sku if strand_sku else line.pp_id
                analysis_item_map[f"won_order_line_detail_id,{line.id}"] = {
                    # 'packing_list_id': self.id,
                    'won_order_lot_id': line.bid_won_order_line_id.id,
                    'won_order_lot_line_id': line.id,
                    'purchase_order_line_id': None,
                    'glot_number': glot_number,
                    'sku_id': sku.id,
                    'packing_qty': 0,
                    'purchase_qty': 0,
                    'difference_qty': 0
                }
        if purchases is None:
            purchases = won_orders.po_id
            
        if not filter_glot_numbers:
            po_lines = purchases.mapped('order_line')
        else:
            po_lines = self.env['purchase.order.line'].search([('glot_number', 'in', filter_glot_numbers), ('order_id', 'in', purchases.ids)])
            
        # 2. 从采购单line填充采购数量
        for po_line in po_lines:
            glot_number = po_line.glot_number
            sku = po_line.product_id
            if sku.type == 'service':
                continue
            key = f"won_order_line_detail_id,{po_line.won_order_line_detail_id.id}"
            if key in analysis_item_map:
                analysis_item_map[key]['purchase_qty'] += po_line.missing_qty
                analysis_item_map[key]['purchase_order_line_id'] = po_line.id
                analysis_item_map[key]['purchase_order_id'] = po_line.order_id.id
            else:
                key = f"purchase_line,{po_line.id}"
                analysis_item_map[key] = {
                    # 'packing_list_id': self.id,
                    'won_order_lot_id': po_line.won_order_line_id.id,
                    'won_order_lot_line_id': po_line.won_order_line_detail_id.id,
                    'purchase_order_line_id': po_line.id,
                    'purchase_order_id': po_line.order_id.id,
                    'glot_number': glot_number,
                    'sku_id': sku.id,
                    'packing_qty': 0,
                    'purchase_qty': po_line.missing_qty,
                    'difference_qty': po_line.missing_qty
                }

        # 删除没有purchase_order_id的记录，这主要是以po为基准生成，忽略中标单的其余项
        analysis_item_map = {k: v for k, v in analysis_item_map.items() if v.get('purchase_order_id', None)}

        # 删除 purchase_qty 为 0 的项
        # analysis_item_map = {k: v for k, v in analysis_item_map.items() if v['purchase_qty'] > 0}

        return analysis_item_map, glot_won_vpi_line_map
