# Copyright 2019 Creu Blanca
# Copyright 2019 Eficent Business and IT Consulting Services S.L.
#     (http://www.eficent.com)
# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html)

from odoo import api, fields, models


class QueueJobBatch(models.Model):
    _inherit = "queue.job.batch"

    tender_name = fields.Char(index=True, help="Tender Name, 用来异步导入标单信息")
    auction_name = fields.Char(index=True, help="Auction Name, 用来异步导入拍卖单信息")
    is_import_tender = fields.Boolean(default=False, help="是否是导入标单的异步操作")
    is_import_auction = fields.Boolean(default=False, help="是否是导入拍卖单的异步操作")
