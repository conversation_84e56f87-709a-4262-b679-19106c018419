/* 一口价购买弹窗样式 */

/* 弹窗遮罩层 */
.hyla-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

/* 弹窗容器 */
.hyla-modal-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

/* 弹窗头部 */
.hyla-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e9ecef;
}

.hyla-modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.hyla-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;
}

.hyla-modal-close:hover {
    background-color: #f8f9fa;
    color: #333;
}

/* 弹窗内容 */
.hyla-modal-body {
    padding: 20px 24px 24px;
}

/* 产品信息区域 */
.hyla-product-info {
    margin-bottom: 24px;
}

.hyla-product-meta {
    margin-bottom: 8px;
}

.hyla-item-number {
    color: #999;
    font-size: 14px;
    float: right;
}

.hyla-product-description {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
    clear: both;
}

/* 购买信息区域 */
.hyla-purchase-section {
    display: flex;
    gap: 40px;
    align-items: flex-start;
}

/* 左侧区域 */
.hyla-purchase-left {
    flex: 1;
}

.hyla-stock-info {
    margin-bottom: 20px;
}

.hyla-label {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.hyla-stock-value {
    font-size: 16px;
    color: #333;
    font-weight: 600;
    margin-left: 8px;
}

/* 购买数量区域 */
.hyla-quantity-section {
    margin-bottom: 20px;
}

.hyla-quantity-section .hyla-label {
    display: block;
    margin-bottom: 8px;
}

.hyla-quantity-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.hyla-quantity-input {
    width: 120px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    text-align: center;
}

.hyla-quantity-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.hyla-quantity-warning {
    color: #ffc107;
    font-size: 16px;
}

.hyla-warning-icon {
    font-size: 18px;
}

/* 右侧区域 */
.hyla-purchase-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

/* 价格区域 */
.hyla-price-section {
    margin-bottom: 24px;
    text-align: right;
}

.hyla-price-row {
    margin-bottom: 12px;
}

.hyla-price-label {
    font-size: 14px;
    color: #666;
    margin-right: 8px;
}

.hyla-price-value {
    font-size: 16px;
    color: #333;
}

.hyla-currency {
    font-size: 14px;
    color: #666;
    margin-left: 8px;
}

.hyla-total-row {
    border-top: 1px solid #e9ecef;
    padding-top: 12px;
}

.hyla-total-label {
    font-size: 16px;
    color: #333;
    font-weight: 600;
    margin-right: 8px;
}

.hyla-total-value {
    font-size: 20px;
    color: #333;
    font-weight: 700;
}

/* 购买按钮 */
.hyla-purchase-btn {
    background: #17a2b8;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 12px 32px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 100px;
}

.hyla-purchase-btn:hover {
    background: #138496;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.hyla-purchase-btn:active {
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hyla-modal-container {
        margin: 10px;
        max-width: none;
    }
    
    .hyla-modal-header {
        padding: 16px 20px 12px;
    }
    
    .hyla-modal-body {
        padding: 16px 20px 20px;
    }
    
    .hyla-purchase-section {
        flex-direction: column;
        gap: 20px;
    }
    
    .hyla-purchase-right {
        align-items: stretch;
    }
    
    .hyla-price-section {
        text-align: left;
    }
    
    .hyla-purchase-btn {
        width: 100%;
    }
    
    .hyla-modal-title {
        font-size: 16px;
    }
    
    .hyla-item-number {
        float: none;
        display: block;
        margin-bottom: 4px;
    }
}

/* 动画效果 */
.hyla-modal-overlay {
    animation: fadeIn 0.3s ease-out;
}

.hyla-modal-container {
    animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
