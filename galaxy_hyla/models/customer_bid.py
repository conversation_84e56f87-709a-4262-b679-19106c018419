from odoo import models, fields, api


class GalaxyHylaCustomerBid(models.Model):
    _name = 'galaxy.hyla.customer.bid'
    _description = 'Galaxy Hyla Customer Bid'

    product_id = fields.Many2one('galaxy.hyla.product.list', string='Product', required=True)
    customer_id = fields.Many2one('res.partner', string='Customer', required=True)
    bid_price = fields.Monetary(string='Bid Price', currency_field='currency_id', required=True)
    # 报价数量
    qty = fields.Integer(string='Quantity', required=True)
    currency_id = fields.Many2one('res.currency', string='Currency', required=True)
    bid_date = fields.Datetime(string='Bid Date', required=True)
    state = fields.Selection([('bid', 'Bidding'), ('won', 'Won'), ('failed', 'Lost')], string='Status', default='bid')
    # 报价类型：一口价、意向价
    bid_type = fields.Selection([('fixed', 'Fixed Price'), ('offer', 'Offer Price')], string='Bid Type',
                                default='fixed')


# 关注模型
class GalaxyHylaCustomerFollow(models.Model):
    _name = 'galaxy.hyla.customer.follow'
    _description = 'Galaxy Hyla Customer Follow'

    customer_id = fields.Many2one('res.partner', string='Customer', required=True)
    product_id = fields.Many2one('galaxy.hyla.product.list', string='Product', required=True)
