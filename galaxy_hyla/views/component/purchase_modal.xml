<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 一口价购买弹窗组件 -->
    <template id="hyla_purchase_modal" name="Hyla Purchase Modal">
        <!-- 弹窗遮罩层 -->
        <div id="purchase-modal" class="hyla-modal-overlay d-none">
            <div class="hyla-modal-container">
                <!-- 弹窗头部 -->
                <div class="hyla-modal-header">
                    <h3 class="hyla-modal-title" id="modal-product-title">Apple iPhone 12 Pro</h3>
                    <button class="hyla-modal-close" onclick="HylaPurchaseModal.close()">
                        <span>×</span>
                    </button>
                </div>
                
                <!-- 弹窗内容 -->
                <div class="hyla-modal-body">
                    <!-- 产品信息 -->
                    <div class="hyla-product-info">
                        <div class="hyla-product-meta">
                            <span class="hyla-item-number" id="modal-item-number">Item#2041813</span>
                        </div>
                        <div class="hyla-product-description" id="modal-product-description">
                            00000004 Apple iPhone 5s 64GB Gold Verizon Unlocked ID OFF HYLA DLS B+ Phone
                        </div>
                    </div>
                    
                    <!-- 购买信息区域 -->
                    <div class="hyla-purchase-section">
                        <!-- 左侧：库存和购买数量 -->
                        <div class="hyla-purchase-left">
                            <div class="hyla-stock-info">
                                <label class="hyla-label">库存数量：</label>
                                <span class="hyla-stock-value" id="modal-stock-quantity">100</span>
                            </div>
                            
                            <div class="hyla-quantity-section">
                                <label class="hyla-label" for="purchase-quantity">购买数量</label>
                                <div class="hyla-quantity-input-group">
                                    <input type="number" 
                                           id="purchase-quantity" 
                                           class="hyla-quantity-input" 
                                           value="100" 
                                           min="1" 
                                           max="100"
                                           onchange="HylaPurchaseModal.updateTotal()"/>
                                    <div class="hyla-quantity-warning">
                                        <span class="hyla-warning-icon">⚠</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧：价格信息 -->
                        <div class="hyla-purchase-right">
                            <div class="hyla-price-section">
                                <div class="hyla-price-row">
                                    <span class="hyla-price-label">标价：</span>
                                    <span class="hyla-price-value">
                                        <span id="modal-unit-price">1500</span>
                                        <span class="hyla-currency">币种：HKD</span>
                                    </span>
                                </div>
                                
                                <div class="hyla-total-row">
                                    <span class="hyla-total-label">总价：</span>
                                    <span class="hyla-total-value" id="modal-total-price">150,000</span>
                                </div>
                            </div>
                            
                            <!-- 购买按钮 -->
                            <button class="hyla-purchase-btn" onclick="HylaPurchaseModal.confirmPurchase()">
                                购买
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>
</odoo>
